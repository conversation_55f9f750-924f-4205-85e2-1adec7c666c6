"""
Test script to process just one page with crash protection
"""

import logging
import os
from services.point_search import PointSearch
from services.point_search import WebDriverRecoveryManager
from config import Config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(funcName)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('single_page_test.log')
    ]
)
logger = logging.getLogger(__name__)

def login_to_site(driver, config):
    """Handle login if required"""
    if config.LOGIN_URL and config.LOGIN_CREDENTIALS:
        try:
            logger.info(f"Logging in at {config.LOGIN_URL}")
            driver.get(config.LOGIN_URL)

            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            driver.find_element(By.NAME, "login_id").send_keys(config.LOGIN_CREDENTIALS["username"])
            driver.find_element(By.NAME, "login_pass").send_keys(config.LOGIN_CREDENTIALS["password"])
            driver.find_element(By.NAME, "login").click()
            WebDriverWait(driver, config.TIMEOUT).until(EC.url_changes(config.LOGIN_URL))
            logger.info("Login successful")
            return True
        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False
    return True

def test_single_page_processing():
    """Test processing a single page with crash protection"""
    logger.info("Starting single page test...")

    # Create recovery manager and driver
    recovery_manager = WebDriverRecoveryManager(max_retries=3, retry_delay=5)
    driver = recovery_manager.create_stable_driver()

    if not driver:
        logger.error("Failed to create driver")
        return False

    try:
        # Load config
        config = Config()

        # Login
        if not login_to_site(driver, config):
            logger.error("Login failed")
            return False

        # Create test output directory
        test_output_dir = "test_single_page_output"
        os.makedirs(test_output_dir, exist_ok=True)

        # Create PointSearch instance
        url = "https://www.en-system.net/admin/tool/schedule_teacher_main.php"
        point_search = PointSearch(driver, url, test_output_dir)

        # Navigate to the page
        logger.info("Navigating to target page...")
        driver.get(url)

        # Test basic page loading
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC

        # Wait for table to load
        logger.info("Waiting for table to load...")
        table = WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.XPATH, "//table[@class='IchiranTbl2']"))
        )

        if table:
            logger.info("✓ Table loaded successfully")

            # Get basic table info
            rows = table.find_elements(By.XPATH, ".//tr")
            logger.info(f"✓ Found {len(rows)} rows in table")

            if len(rows) > 1:
                # Test processing first row only
                logger.info("Testing processing of first row...")

                # Create page folder
                page_folder = os.path.join(test_output_dir, "test_page_1")
                os.makedirs(page_folder, exist_ok=True)

                # Save basic table data
                headers = []
                data = []

                if len(rows) > 0:
                    header_cells = rows[0].find_elements(By.XPATH, ".//th")
                    headers = [cell.text.strip() for cell in header_cells]

                    # Get first data row
                    if len(rows) > 1:
                        cells = rows[1].find_elements(By.XPATH, ".//td")
                        row_data = [cell.text.strip() for cell in cells]
                        data.append(row_data)

                # Save test data
                import json
                table_data = {
                    'page': 'test',
                    'headers': headers,
                    'data': data
                }

                json_file = os.path.join(page_folder, "test_table_data.json")
                with open(json_file, "w", encoding="utf-8") as f:
                    json.dump(table_data, f, ensure_ascii=False, indent=2)

                logger.info("✓ Basic table data saved successfully")

                # Test screenshot and HTML saving
                try:
                    point_search.extractData.save_screenshot(page_folder, "test_screenshot.png")
                    point_search.extractData.save_page_source(page_folder, "test_page.html")
                    logger.info("✓ Screenshot and HTML saved successfully")
                except Exception as e:
                    logger.error(f"✗ Failed to save screenshot/HTML: {e}")

                logger.info("✅ Single page test completed successfully!")
                return True
            else:
                logger.error("✗ No data rows found in table")
                return False
        else:
            logger.error("✗ Table not found")
            return False

    except Exception as e:
        logger.error(f"✗ Single page test failed: {e}")
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return False

    finally:
        if driver:
            try:
                driver.quit()
                logger.info("Driver closed successfully")
            except:
                logger.warning("Could not close driver cleanly")

def main():
    """Run the single page test"""
    print("Single Page Processing Test")
    print("=" * 30)

    success = test_single_page_processing()

    print("\n" + "=" * 30)
    if success:
        print("✅ SINGLE PAGE TEST PASSED!")
        print("The crash protection is working correctly.")
        print("You can now run the full scraping script.")
        print("Check 'test_single_page_output' folder for results.")
    else:
        print("❌ SINGLE PAGE TEST FAILED!")
        print("Check 'single_page_test.log' for detailed error information.")

if __name__ == "__main__":
    main()
