import time
import random
from extract_page import ExtractPage
from csv_writer import CSVWriter
import json
import os
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
import re
import csv
import threading
import time

logger = logging.getLogger(__name__)

class PointSearch:
    # Định nghĩa constants cho các nút
    DETAIL_BUTTON = "基本"
    BUYER_LIST_BUTTON = "週間"
    
    def __init__(self, driver, url, output_dir, buttons=None):
        self.driver = driver
        self.url = url
        self.output_dir = output_dir
        # Đảm bảo buttons chứa cả hai loại nút
        self.buttons = buttons if buttons else [
            self.DETAIL_BUTTON,
            self.BUYER_LIST_BUTTON, 
        ]        
        self.extractData = ExtractPage(driver)
        logger.info(f"Initialized with buttons: {self.buttons}")

    def sanitize_filename(self, name):
        return re.sub(r'[<>:"/\\|?*\n\r\t]', '_', name)

    

    def process_all_pages(self):
        """Process all 50 pages and save data for each page"""
        try:
            # Load trang đầu tiên
            self.driver.get(self.url)
            
            for page_num in range(11, 32):
                try:
                    logger.info(f"Processing page {page_num}")
                    
                    # Tạo thư mục cho trang hiện tại
                    page_folder = os.path.join(self.output_dir, f"page_{page_num}")
                    os.makedirs(page_folder, exist_ok=True)
                    
                    if page_num > 1:
                        # Click vào số trang tương ứng
                        page_link = WebDriverWait(self.driver, 15).until(
                            EC.element_to_be_clickable((By.XPATH, f"//a[text()='{page_num}']"))
                        )
                        self.driver.execute_script("arguments[0].click();", page_link)
                        time.sleep(random.uniform(1, 2))

                    # Đợi bảng refresh
                    table = WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.XPATH, "//table[@class='IchiranTbl2']"))
                    )
                    
                    # LƯU THÔNG TIN TRANG HIỆN TẠI
                    self.current_target_page = page_num
                    self.monitoring_active = True
                    
                    # BẮT ĐẦU MONITOR THREAD
                    monitor_thread = threading.Thread(target=self._monitor_page_redirect, daemon=True)
                    monitor_thread.start()
                    
                    if table:
                        # Xử lý và lưu dữ liệu bảng như bình thường
                        headers = []
                        data = []
                        
                        rows = table.find_elements(By.XPATH, ".//tr")
                        if len(rows) > 0:
                            header_cells = rows[0].find_elements(By.XPATH, ".//th")
                            headers = [cell.text.strip() for cell in header_cells]
                            
                            for row in rows[1:]:
                                cells = row.find_elements(By.XPATH, ".//td")
                                row_data = [cell.text.strip() for cell in cells]
                                data.append(row_data)
                        
                        # Lưu các file như bình thường
                        table_data = {
                            'page': page_num,
                            'headers': headers,
                            'data': data
                        }
                        
                        # Lưu JSON
                        json_file = os.path.join(page_folder, f"page_{page_num}.json")
                        with open(json_file, "w", encoding="utf-8") as f:
                            json.dump(table_data, f, ensure_ascii=False, indent=2)
                        
                        # Lưu CSV    
                        csv_file = os.path.join(page_folder, f"page_{page_num}.csv")
                        with open(csv_file, "w", encoding="utf-8", newline='') as f:
                            writer = csv.writer(f)
                            writer.writerow(headers)
                            writer.writerows(data)
                        
                        # Lưu screenshot và HTML
                        self.extractData.save_screenshot(page_folder, f"page_{page_num}.png")
                        self.extractData.save_page_source(page_folder, f"page_{page_num}.html")
                        
                        # XỬ LÝ CHI TIẾT
                        try:
                            logger.info(f"Starting detailed processing for page {page_num}")
                            self.process_detailed_point_search(table, page_folder)
                            logger.info(f"Completed detailed processing for page {page_num}")
                            
                        except Exception as detail_error:
                            logger.error(f"Error in detailed processing for page {page_num}: {detail_error}")
                        
                        # TẮT MONITORING
                        self.monitoring_active = False
                        
                        # KIỂM TRA CUỐI CÙNG VÀ REDIRECT NẾU CẦN
                        self._ensure_correct_page_final(page_num)

                        logger.info(f"Successfully processed page {page_num}")
                        time.sleep(random.uniform(1, 2))
                        
                except Exception as page_error:
                    logger.error(f"Error processing page {page_num}: {page_error}")
                    self.monitoring_active = False
                    continue
                    
        except Exception as e:
            logger.error(f"Error in process_all_pages: {e}")

    def _monitor_page_redirect(self):
        """Monitor và tự động redirect khi về trang 1"""
        while getattr(self, 'monitoring_active', False):
            try:
                # Kiểm tra trang hiện tại
                current_page = self._get_current_page_from_pagination()
                target_page = getattr(self, 'current_target_page', 1)
                
                if current_page == 1 and target_page > 1:
                    logger.warning(f"DETECTED AUTO-REDIRECT TO PAGE 1! Target should be page {target_page}")
                    logger.info(f"Auto-redirecting back to page {target_page}")
                    
                    # Ngay lập tức click về trang đích
                    try:
                        page_link = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, f"//a[text()='{target_page}']"))
                        )
                        self.driver.execute_script("arguments[0].click();", page_link)
                        time.sleep(1)
                        
                        # Verify đã về đúng trang
                        WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, "//table[@class='IchiranTbl2']"))
                        )
                        
                        logger.info(f"Successfully auto-redirected back to page {target_page}")
                        
                    except Exception as redirect_error:
                        logger.error(f"Auto-redirect failed: {redirect_error}")
                    
                time.sleep(0.5)  # Check mỗi 0.5 giây
                
            except Exception as e:
                logger.error(f"Error in monitoring: {e}")
                time.sleep(1)

    def _get_current_page_from_pagination(self):
        """Lấy số trang hiện tại từ pagination"""
        try:
            # Tìm element active trong pagination
            active_elements = self.driver.find_elements(By.XPATH, 
                "//a[contains(@class, 'active') or contains(@class, 'current') or contains(@class, 'selected')] | "
                "//span[contains(@class, 'active') or contains(@class, 'current') or contains(@class, 'selected')]"
            )
            
            for element in active_elements:
                text = element.text.strip()
                if text.isdigit():
                    return int(text)
            
            return 1  # Default nếu không tìm thấy
            
        except Exception as e:
            logger.error(f"Error getting current page: {e}")
            return 1

    def _ensure_correct_page_final(self, target_page):
        """Kiểm tra cuối cùng và đảm bảo ở đúng trang"""
        try:
            current_page = self._get_current_page_from_pagination()
            
            if current_page != target_page:
                logger.warning(f"Final check: at page {current_page}, should be page {target_page}")
                
                # Click về trang đúng
                page_link = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, f"//a[text()='{target_page}']"))
                )
                self.driver.execute_script("arguments[0].click();", page_link)
                time.sleep(1)
                
                # Verify
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//table[@class='IchiranTbl2']"))
                )
                
                logger.info(f"Final correction: successfully returned to page {target_page}")
                
        except Exception as e:
            logger.error(f"Error in final page check: {e}")


    def process_detailed_point_search(self, table, page_folder):
        import json
        import requests
        import time
        import random
        from urllib.parse import urlparse, parse_qs, urlencode
        
        def capture_api_urls(driver, max_retries=5):
            """Capture API URLs với retry mechanism và multiple methods"""
            api_urls = {"基本": None, "週間": None}
            
            for retry in range(max_retries):
                try:
                    # Method 1: Performance API
                    logs = driver.execute_script("""
                        var performance = window.performance || window.mozPerformance || window.msPerformance || window.webkitPerformance || {};
                        var entries = performance.getEntries() || [];
                        return Array.from(entries).map(entry => ({
                            name: entry.name || '',
                            responseEnd: entry.responseEnd || 0,
                            initiatorType: entry.initiatorType || ''
                        })).sort((a, b) => b.responseEnd - a.responseEnd);
                    """)
                    
                    # Tìm URLs trong logs
                    for entry in logs:
                        url = entry.get('name', '')
                        if 'schedule_teacher_base_json-events.php' in url:
                            api_urls['基本'] = url
                            logger.info(f"Found 基本 API URL: {url}")
                        elif 'schedule_teacher_json-events.php' in url:
                            api_urls['週間'] = url
                            logger.info(f"Found 週間 API URL: {url}")
                    
                    # Method 2: Nếu không tìm thấy, thử với XMLHttpRequest monitoring
                    if not any(api_urls.values()):
                        driver.execute_script("""
                            window.capturedRequests = window.capturedRequests || [];
                            var originalXHR = window.XMLHttpRequest;
                            window.XMLHttpRequest = function() {
                                var xhr = new originalXHR();
                                var originalOpen = xhr.open;
                                xhr.open = function(method, url) {
                                    if (url.includes('json-events.php')) {
                                        window.capturedRequests.push(url);
                                    }
                                    return originalOpen.apply(this, arguments);
                                };
                                return xhr;
                            };
                        """)
                        
                        time.sleep(2)  # Đợi requests
                        
                        captured = driver.execute_script("return window.capturedRequests || [];")
                        for url in captured:
                            if 'schedule_teacher_base_json-events.php' in url:
                                api_urls['基本'] = url
                            elif 'schedule_teacher_json-events.php' in url:
                                api_urls['週間'] = url
                    
                    # Nếu tìm thấy ít nhất 1 URL, break
                    if any(api_urls.values()):
                        break
                        
                    logger.warning(f"Retry {retry + 1}: No API URLs found, waiting...")
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"Error capturing API URLs (attempt {retry + 1}): {e}")
                    time.sleep(1)
            
            return api_urls

        def generate_fallback_api_url(teacher_id, button_type):
            """Tạo API URL fallback với teacher_id từ trang hiện tại"""
            try:
                # Lấy teacher_id từ URL hiện tại nếu có
                current_url = self.driver.current_url
                if 'teacher_id=' in current_url:
                    import re
                    match = re.search(r'teacher_id=(\d+)', current_url)
                    if match:
                        teacher_id = match.group(1)
            except:
                pass
            
            base_url = "https://www.en-system.net/admin/tool/"
            current_time = int(time.time() * 1000)
            
            if button_type == self.DETAIL_BUTTON:
                endpoint = "schedule_teacher_base_json-events.php"
            else:
                endpoint = "schedule_teacher_json-events.php"
            
            return f"{base_url}{endpoint}?teacher_id={teacher_id}&start=0&end=1893456000&_={current_time}"

        def modify_api_url(url):
            """Modify API URL với error handling"""
            try:
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                
                # Đảm bảo có teacher_id
                if 'teacher_id' not in params:
                    logger.warning("No teacher_id in URL, trying to extract from current page")
                    current_url = self.driver.current_url
                    if 'teacher_id=' in current_url:
                        import re
                        match = re.search(r'teacher_id=(\d+)', current_url)
                        if match:
                            params['teacher_id'] = [match.group(1)]
                
                # Set parameters
                params['start'] = ['0']
                params['end'] = ['1893456000']
                params['_'] = [str(int(time.time() * 1000))]
                
                new_query = urlencode(params, doseq=True)
                modified_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}?{new_query}"
                
                logger.info(f"Modified URL: {modified_url}")
                return modified_url
                
            except Exception as e:
                logger.error(f"Error modifying API URL: {e}")
                return url

        def fetch_api_data(url, driver, max_retries=3):
            """Fetch API data với multiple methods và retry"""
            for retry in range(max_retries):
                try:
                    # Method 1: Requests với session cookies
                    selenium_cookies = driver.get_cookies()
                    cookies = {cookie['name']: cookie['value'] for cookie in selenium_cookies}
                    
                    headers = {
                        'User-Agent': driver.execute_script("return navigator.userAgent;"),
                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                        'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Referer': driver.current_url,
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                    
                    response = requests.get(url, headers=headers, cookies=cookies, timeout=30)
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            # Validate data
                            if data is not None and not (isinstance(data, dict) and 'error' in data):
                                logger.info(f"Successfully fetched API data via requests (attempt {retry + 1})")
                                return data
                        except json.JSONDecodeError:
                            logger.warning(f"Invalid JSON response (attempt {retry + 1})")
                    else:
                        logger.warning(f"HTTP {response.status_code} (attempt {retry + 1})")
                    
                except Exception as e:
                    logger.error(f"Requests method failed (attempt {retry + 1}): {e}")
                
                # Method 2: Fetch từ browser
                try:
                    logger.info(f"Trying browser fetch method (attempt {retry + 1})")
                    api_data = driver.execute_script(f"""
                        return new Promise((resolve) => {{
                            fetch('{url}', {{
                                method: 'GET',
                                credentials: 'include',
                                headers: {{
                                    'X-Requested-With': 'XMLHttpRequest',
                                    'Accept': 'application/json',
                                    'Cache-Control': 'no-cache'
                                }}
                            }})
                            .then(response => response.json())
                            .then(data => resolve(data))
                            .catch(error => {{
                                console.error('Fetch error:', error);
                                resolve(null);
                            }});
                        }});
                    """)
                    
                    if api_data:
                        logger.info(f"Successfully fetched API data via browser (attempt {retry + 1})")
                        return api_data
                        
                except Exception as e:
                    logger.error(f"Browser fetch failed (attempt {retry + 1}): {e}")
                
                # Exponential backoff
                if retry < max_retries - 1:
                    wait_time = (2 ** retry) + random.uniform(0, 1)
                    logger.info(f"Waiting {wait_time:.1f}s before retry...")
                    time.sleep(wait_time)
            
            return None

        def safe_save_json(data, filepath):
            """Safely save JSON data với backup"""
            try:
                # Tạo backup nếu file đã tồn tại
                if os.path.exists(filepath):
                    backup_path = f"{filepath}.backup"
                    import shutil
                    shutil.copy2(filepath, backup_path)
                
                # Save JSON
                with open(filepath, "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                # Verify file was written correctly
                with open(filepath, "r", encoding="utf-8") as f:
                    json.load(f)  # Test if valid JSON
                
                logger.info(f"Successfully saved JSON to {filepath}")
                return True
                
            except Exception as e:
                logger.error(f"Error saving JSON to {filepath}: {e}")
                return False

        # Main processing logic
        rows = table.find_elements(By.XPATH, ".//tr")
        logger.info(f"Number of rows: {len(rows)}")

        try:
            for index in range(1, len(rows)):
                try:
                    # Refresh table
                    current_table = WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.XPATH, "//table[@summary='スケジュール' and @class='IchiranTbl2']"))
                    )
                    current_rows = current_table.find_elements(By.XPATH, ".//tr")
                    
                    if index >= len(current_rows):
                        logger.warning(f"Index {index} exceeds available rows")
                        continue
                    
                    row = current_rows[index]
                    cells = row.find_elements(By.XPATH, ".//td")
                    
                    if len(cells) == 0:
                        continue
                    
                    row_id = cells[0].text
                    
                    # Create folder
                    timestamp = int(time.time())
                    main_folder_name = f"schedule_teacher_main_{row_id}_{timestamp}"
                    record_path = os.path.join(page_folder, main_folder_name)
                    os.makedirs(record_path, exist_ok=True)
                    logger.info(f"Created folder for ID {row_id}: {record_path}")
                    
                    # Find buttons với multiple selectors
                    buttons = []
                    
                    # Detail button selectors
                    detail_selectors = [
                        f".//input[@type='button' and @value='{self.DETAIL_BUTTON}']",
                        f".//input[@value='{self.DETAIL_BUTTON}']",
                        f".//button[text()='{self.DETAIL_BUTTON}']",
                        ".//input[contains(@onclick, 'detail') or contains(@onclick, 'base')]"
                    ]
                    
                    for selector in detail_selectors:
                        try:
                            detail_btn = row.find_element(By.XPATH, selector)
                            buttons.append((detail_btn, self.DETAIL_BUTTON))
                            logger.info(f"Found detail button for ID {row_id}")
                            break
                        except:
                            continue
                    
                    # Buyer button selectors
                    buyer_selectors = [
                        f".//input[@type='button' and @value='{self.BUYER_LIST_BUTTON}']",
                        f".//input[@value='{self.BUYER_LIST_BUTTON}']",
                        f".//button[text()='{self.BUYER_LIST_BUTTON}']",
                        ".//input[contains(@onclick, 'week') or contains(@onclick, 'buyer')]"
                    ]
                    
                    for selector in buyer_selectors:
                        try:
                            buyer_btn = row.find_element(By.XPATH, selector)
                            buttons.append((buyer_btn, self.BUYER_LIST_BUTTON))
                            logger.info(f"Found buyer button for ID {row_id}")
                            break
                        except:
                            continue
                    
                    if len(buttons) == 0:
                        logger.warning(f"No buttons found for ID {row_id}")
                        continue
                    
                    logger.info(f"Total buttons to process for ID {row_id}: {len(buttons)}")
                    
                    folder_mapping = {self.DETAIL_BUTTON: "基本", self.BUYER_LIST_BUTTON: "週間"}
                    
                    # Process each button
                    for button_element, button_value in buttons:
                        try:
                            # Refresh row
                            current_table = WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, "//table[@summary='スケジュール' and @class='IchiranTbl2']"))
                            )
                            current_rows = current_table.find_elements(By.XPATH, ".//tr")
                            
                            if index >= len(current_rows):
                                logger.error(f"Row index {index} no longer exists")
                                break
                            
                            current_row = current_rows[index]
                            
                            # Find button again
                            current_button = None
                            selectors = detail_selectors if button_value == self.DETAIL_BUTTON else buyer_selectors
                            for selector in selectors:
                                try:
                                    current_button = current_row.find_element(By.XPATH, selector)
                                    break
                                except:
                                    continue
                            
                            if not current_button:
                                logger.error(f"Button '{button_value}' not found in refreshed row")
                                continue
                            
                            button_folder = os.path.join(record_path, folder_mapping[button_value])
                            os.makedirs(button_folder, exist_ok=True)
                            logger.info(f"Processing button '{button_value}' for ID {row_id}")
                            
                            # Clear performance logs
                            try:
                                self.driver.execute_script("window.performance.clearResourceTimings();")
                                self.driver.execute_script("window.capturedRequests = [];")
                            except:
                                pass
                            
                            # Click button
                            click_success = False
                            for attempt in range(3):
                                try:
                                    self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", current_button)
                                    time.sleep(1)
                                    self.driver.execute_script("arguments[0].click();", current_button)
                                    time.sleep(random.uniform(1, 2))  # Tăng thời gian đợi
                                    click_success = True
                                    break
                                except Exception as click_error:
                                    logger.warning(f"Click attempt {attempt + 1} failed: {click_error}")
                                    time.sleep(2)
                            
                            if not click_success:
                                logger.error(f"Failed to click button '{button_value}' after 3 attempts")
                                continue
                            
                            # Capture API URLs
                            api_urls = capture_api_urls(self.driver)
                            
                            # Get API URL
                            api_url = api_urls.get(folder_mapping[button_value])
                            if not api_url:
                                api_url = generate_fallback_api_url(row_id, button_value)
                                logger.info(f"Using fallback API URL: {api_url}")
                            else:
                                api_url = modify_api_url(api_url)
                            
                            # Fetch API data
                            api_data = fetch_api_data(api_url, self.driver)
                            
                            if api_data:
                                # Save API data
                                api_json_file = os.path.join(button_folder, "api_data.json")
                                if safe_save_json(api_data, api_json_file):
                                    # Save URL info
                                    api_url_info = {
                                        "api_url": api_url,
                                        "timestamp": timestamp,
                                        "button_type": button_value,
                                        "teacher_id": row_id,
                                        "data_count": len(api_data) if isinstance(api_data, list) else 1,
                                        "method": "captured" if api_urls.get(folder_mapping[button_value]) else "fallback"
                                    }
                                    
                                    api_url_file = os.path.join(button_folder, "api_url_info.json")
                                    safe_save_json(api_url_info, api_url_file)
                                    
                                    logger.info(f"Successfully saved API data for {button_value}")
                                else:
                                    logger.error(f"Failed to save API data for {button_value}")
                            else:
                                logger.warning(f"No API data received for {button_value}")
                            
                            # Extract form data
                            try:
                                WebDriverWait(self.driver, 15).until(
                                    lambda driver: driver.find_element(By.ID, "mntform") or 
                                                driver.find_element(By.NAME, "mntform") or
                                                driver.find_element(By.TAG_NAME, "form")
                                )
                                form_data = self.extractData.extract_form_data()
                            except Exception as form_error:
                                logger.error(f"Error extracting form data for {button_value}: {form_error}")
                                form_data = {"error": f"Could not extract data for {button_value}"}
                            
                                try:
                                    self.extractData.save_screenshot(button_folder, "screenshot.png")
                                    self.extractData.save_page_source(button_folder, "page.html")
                                except Exception as save_error:
                                    logger.error(f"Error saving screenshot/HTML: {save_error}")
                                
                                logger.info(f"Successfully saved form data for '{button_value}' (ID: {row_id})")
                            
                            # Go back
                            back_success = False
                            for attempt in range(3):
                                try:
                                    back_button = WebDriverWait(self.driver, 10).until(
                                        EC.element_to_be_clickable((By.XPATH, ".//input[@type='button' and @value='戻る']"))
                                    )
                                    self.driver.execute_script("arguments[0].click();", back_button)
                                    time.sleep(random.uniform(1, 2))
                                    
                                    WebDriverWait(self.driver, 10).until(
                                        EC.presence_of_element_located((By.XPATH, "//table[@summary='スケジュール' and @class='IchiranTbl2']"))
                                    )
                                    back_success = True
                                    break
                                except Exception as back_error:
                                    logger.warning(f"Back attempt {attempt + 1} failed: {back_error}")
                                    time.sleep(3)
                            
                            if not back_success:
                                logger.error(f"Failed to go back after processing '{button_value}'")
                                self.driver.get(self.url)
                                time.sleep(random.uniform(1, 2))
                                break
                            
                        except Exception as button_error:
                            logger.error(f"Error processing button {button_value} for ID {row_id}: {button_error}")
                            try:
                                self.driver.get(self.url)
                                time.sleep(random.uniform(1, 2))
                            except:
                                pass
                            continue
                    
                    logger.info(f"Completed processing all buttons for ID {row_id}")
                    
                except Exception as row_error:
                    logger.error(f"Error processing row {index}: {row_error}")
                    continue
        
        except Exception as e:
            logger.error(f"Error in process_detailed_point_search: {e}")
        
    